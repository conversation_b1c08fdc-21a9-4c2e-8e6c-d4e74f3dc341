{"[dart]": {"editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.suggest.insertMode": "replace", "editor.defaultFormatter": "Dart-Code.dart-code", "editor.inlayHints.enabled": "offUnlessPressed", "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}}