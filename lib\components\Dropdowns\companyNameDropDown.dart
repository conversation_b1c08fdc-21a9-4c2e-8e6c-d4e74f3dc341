import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:dropdown_search/dropdown_search.dart';
import '../../resourses/resourses.dart';

class CompanyName {
  static int? companyId;
}

class CompanyNameDropdown extends StatefulWidget {
  final String? initialValue;
  final int? leadId;

  const CompanyNameDropdown({
    Key? key,
    this.initialValue,
    this.leadId,
  }) : super(key: key);

  @override
  _CompanyNameDropdownState createState() => _CompanyNameDropdownState();
}

class _CompanyNameDropdownState extends State<CompanyNameDropdown> {
  List<dynamic> _totalLeadList = [];
  bool isLoading = true;

  String? _selectedCompanyName;
  int? _selectedCompanyId;

  @override
  void initState() {
    super.initState();
    getCompanyName();
  }

  Future<void> getCompanyName() async {
    try {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      String? token = sharedPreferences.getString("token");
      String? userId = sharedPreferences.getString("id");

      final response = await http.post(
        Uri.parse("https://crm.ihelpbd.com/api/crm-lead-data-show"),
        headers: {
          'Authorization': 'Bearer $token',
          'user_id': '$userId',
        },
        body: {
          'start_date': '',
          'end_date': '',
          'user_id_search': userId,
          'session_user_id': userId,
          'lead_pipeline_id': '',
          'lead_source_id': '',
          'searchData': '',
          'is_type': '0',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        setState(() {
          _totalLeadList = responseData['data'];

          if (widget.leadId != null) {
            final companyWithLeadId = _totalLeadList.firstWhere(
              (company) => company['id'] == widget.leadId,
              orElse: () => null,
            );
            if (companyWithLeadId != null) {
              _onCompanySelected(companyWithLeadId);
            }
          } else if (widget.initialValue != null) {
            final initialCompany = _totalLeadList.firstWhere(
              (company) => company['company_name'] == widget.initialValue,
              orElse: () => null,
            );
            if (initialCompany != null) {
              _onCompanySelected(initialCompany);
            }
          }
        });
      } else {
        print('Failed to fetch Company data');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print("Error fetching company data: $e");
    }
  }

  void _onCompanySelected(dynamic selectedCompany) {
    setState(() {
      _selectedCompanyName = selectedCompany['company_name'];
      _selectedCompanyId = selectedCompany['id'];
      CompanyName.companyId = _selectedCompanyId;
    });

    print('Selected company id (static): ${CompanyName.companyId}');
  }

  @override
  Widget build(BuildContext context) {
    return _totalLeadList.isNotEmpty
        ? Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: DropdownSearch<dynamic>(

              popupProps: PopupProps.menu(
                showSearchBox: true,
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    hintText: "Search company...",
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
              items: _totalLeadList,
              itemAsString: (company) => company['company_name'],
              
              decoratorProps: DropDownDecoratorProps(

                decoration: InputDecoration(
                  hintText: "Select company name",
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 10),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF8F6F8),
                ),
              ),
              onChanged: (value) => _onCompanySelected(value),
              selectedItem: _selectedCompanyName != null
                  ? _totalLeadList.firstWhere((company) =>
                      company['company_name'] == _selectedCompanyName)
                  : null,
              validator: (value) =>
                  value == null ? 'Company name is required' : null,
            ),
          )
        : R.appSpinKits.spinKitFadingCube;
  }
}